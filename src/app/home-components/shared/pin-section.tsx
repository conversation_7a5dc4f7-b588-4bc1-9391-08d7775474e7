"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { App } from "@/core.constants";
import { CollapsibleSection } from "./collapsible-section";
import { Hash, ExternalLink } from "lucide-react";

interface PinSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const PinSection = ({ app, variant = "drawer" }: PinSectionProps) => {
  if (!app.pin?.enabled) return null;

  const isModal = variant === "modal";

  return (
    <CollapsibleSection
      icon={Hash}
      title="PIN"
      iconColorClass={
        isModal
          ? "bg-gradient-to-r from-green-500 to-blue-500"
          : "text-green-500"
      }
      variant={variant}
    >
      <div
        className={
          isModal
            ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
            : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
        }
      >
        <p
          className={
            isModal
              ? "text-neutral-300 mb-4"
              : "text-neutral-700 dark:text-neutral-300 mb-4"
          }
        >
          This app supports PIN or Passkey authentication for enhanced security.
        </p>

        {app.pin.link && (
          <Button
            asChild
            className={
              isModal
                ? "w-full max-w-[270px] bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                : "w-full justify-start bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 h-12  text-white"
            }
            variant={isModal ? "default" : "outline"}
          >
            <Link
              href={app.pin.link}
              target="_blank"
              className="flex items-center justify-center"
            >
              <Hash className="w-4 h-4 mr-3" />
              <span className="flex-1">Setup PIN / Passkey</span>
              <ExternalLink className="w-4 h-4 ml-3 opacity-70" />
            </Link>
          </Button>
        )}
      </div>
    </CollapsibleSection>
  );
};
