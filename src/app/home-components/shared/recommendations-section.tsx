"use client";

import React from "react";
import { Markdown } from "@/components/ui/markdown";
import { App } from "@/core.constants";
import { CollapsibleSection } from "./collapsible-section";
import { Lightbulb } from "lucide-react";

interface RecommendationsSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const RecommendationsSection = ({
  app,
  variant = "drawer",
}: RecommendationsSectionProps) => {
  if (!app.recommendations) return null;

  const isModal = variant === "modal";

  return (
    <CollapsibleSection
      icon={Lightbulb}
      title="Recommendations"
      iconColorClass={
        isModal
          ? "bg-gradient-to-r from-blue-500 to-purple-500"
          : "text-blue-500"
      }
      variant={variant}
    >
      <div
        className={
          isModal
            ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
            : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
        }
      >
        <Markdown
          content={app.recommendations}
          className={
            isModal
              ? "text-neutral-300"
              : "text-neutral-700 dark:text-neutral-300"
          }
        />
      </div>
    </CollapsibleSection>
  );
};
