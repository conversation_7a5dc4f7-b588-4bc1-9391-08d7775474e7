"use client";

import React from "react";
import {
  Use<PERSON>orm<PERSON>eg<PERSON>,
  FieldErrors,
  UseFormWatch,
  UseFormSetValue,
} from "react-hook-form";
import Image from "next/image";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileInput } from "@/components/ui/file-input";
import { AppType, APP_TYPE_TEXT } from "@/core.constants";

export interface AppFormData {
  name: string;
  type: AppType;
  appLogo?: string;
  yubikeys: { enabled: boolean; link?: string; description?: string };
  phoneNumber: { enabled: boolean; link?: string; description?: string };
  backup: { enabled: boolean; link?: string };
  pin: { enabled: boolean; link?: string; description?: string };
  healthAccount: string;
  recommendations?: string;
  emergency: {
    links: Array<{ link?: string; description?: string; label?: string }>;
    instructions: Array<{ description: string; imageLink?: string }>;
  };
}

interface BasicInfoSectionProps {
  register: UseFormRegister<AppFormData>;
  errors: FieldErrors<AppFormData>;
  watch: UseFormWatch<AppFormData>;
  setValue: UseFormSetValue<AppFormData>;
  onLogoUpload: (file: File) => Promise<void>;
  uploadingLogo: boolean;
}

export const BasicInfoSection = ({
  register,
  errors,
  watch,
  setValue,
  onLogoUpload,
  uploadingLogo,
}: BasicInfoSectionProps) => {
  const handleTypeChange = (newType: AppType) => {
    setValue("type", newType);
  };

  const handleRemoveLogo = () => {
    setValue("appLogo", "");
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2">
        Basic Information
      </h3>

      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name" className="text-sm font-medium">
            App Name
          </Label>
          <Input
            id="name"
            {...register("name")}
            placeholder="Enter app name"
            className="h-11"
          />
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="type" className="text-sm font-medium">
            App Type
          </Label>
          <Select value={watch("type")} onValueChange={handleTypeChange}>
            <SelectTrigger className="h-11">
              <SelectValue placeholder="Select app type" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(APP_TYPE_TEXT).map(([key, value]) => (
                <SelectItem key={key} value={key}>
                  {value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.type && (
            <p className="text-sm text-red-600">{errors.type.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium">App Logo (Optional)</Label>
        <FileInput
          onFileSelect={onLogoUpload}
          accept="image/*"
          disabled={uploadingLogo}
          loading={uploadingLogo}
          currentFile={watch("appLogo")}
          onRemoveFile={handleRemoveLogo}
          placeholder="Upload app logo"
        />
        {watch("appLogo") && (
          <div className="mt-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
            <p className="text-sm font-medium mb-2">Preview:</p>
            <Image
              src={watch("appLogo")!}
              alt="App Logo"
              width={64}
              height={64}
              className="w-16 h-16 object-contain border rounded-lg bg-white"
            />
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="recommendations" className="text-sm font-medium">
          Recommendations (Optional)
        </Label>
        <Textarea
          id="recommendations"
          {...register("recommendations")}
          placeholder="Enter app recommendations or additional notes"
          className="min-h-[100px]"
        />
        {errors.recommendations && (
          <p className="text-sm text-red-600">
            {errors.recommendations.message}
          </p>
        )}
      </div>
    </div>
  );
};
